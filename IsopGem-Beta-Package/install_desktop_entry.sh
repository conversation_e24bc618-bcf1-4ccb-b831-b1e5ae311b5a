#!/bin/bash

# IsopGem Desktop Entry Installer
# Creates a desktop shortcut for IsopGem

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🖥️  IsopGem Desktop Entry Installer"
echo "===================================="

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ISOPGEM_DIR="$SCRIPT_DIR/IsopGem"
EXECUTABLE="$ISOPGEM_DIR/IsopGem"
LAUNCHER_SCRIPT="$SCRIPT_DIR/run_isopgem.sh"

# Check if files exist
if [ ! -f "$EXECUTABLE" ]; then
    print_error "IsopGem executable not found at: $EXECUTABLE"
    exit 1
fi

if [ ! -f "$LAUNCHER_SCRIPT" ]; then
    print_error "Launcher script not found at: $LAUNCHER_SCRIPT"
    exit 1
fi

# Create desktop entry directory if it doesn't exist
DESKTOP_DIR="$HOME/.local/share/applications"
mkdir -p "$DESKTOP_DIR"

# Desktop entry file
DESKTOP_FILE="$DESKTOP_DIR/isopgem-beta.desktop"

print_status "Creating desktop entry at: $DESKTOP_FILE"

# Create the desktop entry
cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=IsopGem (Beta)
Comment=Gematria, Sacred Geometry, and Esoteric Analysis Tool
Exec=$LAUNCHER_SCRIPT
Icon=$SCRIPT_DIR/IsopGem/_internal/resources/images/icon.png
Terminal=false
StartupNotify=true
Categories=Education;Science;
Keywords=gematria;geometry;astrology;numerology;
StartupWMClass=IsopGem
EOF

# Make the desktop file executable
chmod +x "$DESKTOP_FILE"

print_success "Desktop entry created successfully!"
print_status "IsopGem should now appear in your applications menu"
print_status "You can also find it by searching for 'IsopGem'"

# Try to update desktop database
if command -v update-desktop-database >/dev/null 2>&1; then
    print_status "Updating desktop database..."
    update-desktop-database "$DESKTOP_DIR" 2>/dev/null || true
fi

echo ""
echo "✅ Installation complete!"
echo ""
echo "You can now:"
echo "  • Find IsopGem in your applications menu"
echo "  • Search for 'IsopGem' in your desktop environment"
echo "  • Run it directly with: $LAUNCHER_SCRIPT"
echo ""
echo "To uninstall the desktop entry:"
echo "  rm '$DESKTOP_FILE'"
