# IsopGem Beta Testing Guide

Thank you for helping test IsopGem! This guide will help you systematically test the application and provide valuable feedback.

## Getting Started

1. **Extract the package** to a location with at least 8GB free space
2. **Run the launcher**: `./run_isopgem.sh`
3. **Wait for startup** - First launch may take 10-15 seconds

## Testing Checklist

### ✅ Basic Functionality

#### Application Startup
- [ ] Application launches without errors
- [ ] All tabs are visible (Gematria, Geometry, Documents, Astrology, TQ)
- [ ] No crash dialogs or error messages
- [ ] Interface appears correctly (no missing elements)

#### Gematria Tab
- [ ] Enter text in the input field
- [ ] Try different cipher methods (Hebrew, Greek, English)
- [ ] Test the "Calculate" button
- [ ] Check if results appear correctly
- [ ] Try the "Save Calculation" feature
- [ ] Test the search functionality
- [ ] Open the Gematria Explorer window

#### Geometry Tab
- [ ] Canvas displays correctly
- [ ] Try drawing basic shapes
- [ ] Test the Regular Polygon Calculator
- [ ] Check shape manipulation tools
- [ ] Test the Golden Ratio tools

#### Documents Tab
- [ ] Try loading a text file (.txt)
- [ ] Try loading a PDF file (if available)
- [ ] Try loading a Word document (.docx, if available)
- [ ] Test document analysis features
- [ ] Check document search functionality

#### Astrology Tab
- [ ] Enter birth data (date, time, location)
- [ ] Generate a chart
- [ ] Check if chart displays correctly
- [ ] Test different chart types

#### TQ Tab
- [ ] Explore the ternary matrix
- [ ] Try different TQ calculations
- [ ] Test the grid explorer

### ✅ Advanced Features

#### Window Management
- [ ] Open multiple tool windows
- [ ] Check if windows stay on top correctly
- [ ] Test window resizing
- [ ] Close and reopen windows

#### Data Persistence
- [ ] Save some calculations
- [ ] Close and restart the application
- [ ] Check if saved data persists

#### Performance Testing
- [ ] Enter very long text in gematria
- [ ] Load a large document
- [ ] Open many windows simultaneously
- [ ] Note any slowdowns or freezes

### ✅ Error Scenarios

#### Invalid Input
- [ ] Enter special characters in gematria
- [ ] Try loading corrupted files
- [ ] Enter invalid dates in astrology
- [ ] Test with empty inputs

#### Resource Limits
- [ ] Try loading very large files
- [ ] Test with minimal system resources
- [ ] Check behavior when disk space is low

## What to Report

### For Each Issue Found:

1. **Steps to Reproduce**
   - Exact sequence of actions
   - What you clicked/typed
   - Which tab you were on

2. **Expected vs Actual Behavior**
   - What you expected to happen
   - What actually happened

3. **System Information**
   - Linux distribution (Ubuntu, Fedora, etc.)
   - Version number
   - Desktop environment (GNOME, KDE, etc.)

4. **Screenshots/Logs**
   - Screenshots of errors
   - Log files from `logs/` directory
   - Any error messages

### Performance Notes
- Startup time
- Response time for calculations
- Memory usage (if you can check)
- Any crashes or freezes

### Usability Feedback
- Is the interface intuitive?
- Are features easy to find?
- Any confusing elements?
- Suggestions for improvement

## Sample Test Scenarios

### Scenario 1: Basic Gematria Calculation
1. Go to Gematria tab
2. Enter "Hello World" in the text field
3. Select "English Ordinal" cipher
4. Click Calculate
5. Verify results appear
6. Try saving the calculation

### Scenario 2: Document Analysis
1. Go to Documents tab
2. Load a text file
3. Click "Analyze Document"
4. Check if gematria analysis appears
5. Try searching within the document

### Scenario 3: Geometry Exploration
1. Go to Geometry tab
2. Open Regular Polygon Calculator
3. Enter 6 sides, radius 100
4. Check if calculations are correct
5. Try generating the shape

### Scenario 4: Multi-Window Workflow
1. Open Gematria Explorer
2. Open TQ Grid Explorer
3. Open Regular Polygon Calculator
4. Switch between windows
5. Check if all remain functional

## Common Issues to Watch For

- **Slow startup** - Note how long it takes
- **Missing fonts** - Hebrew/Greek characters not displaying
- **Window positioning** - Windows opening off-screen
- **Memory leaks** - Application getting slower over time
- **File permissions** - Unable to save data
- **Network issues** - Astrology features not working

## Providing Feedback

When reporting issues, please:
1. Check the `logs/` directory for error logs
2. Include your system information
3. Be as specific as possible
4. Include screenshots when helpful

## Thank You!

Your testing helps make IsopGem better for everyone. Every bug report and suggestion is valuable, no matter how small!

---

**Happy Testing!** 🧪✨
