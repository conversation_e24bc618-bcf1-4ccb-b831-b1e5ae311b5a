# IsopGem Beta - Quick Start Guide

## 🚀 Get Started in 3 Steps

### 1. Extract the Package
```bash
# If you downloaded the .tar.gz file:
tar -xzf IsopGem-Beta-20250605.tar.gz

# If you downloaded the .zip file:
unzip IsopGem-Beta-20250605.zip
```

### 2. Navigate to the Directory
```bash
cd IsopGem-Beta-Package
```

### 3. Run IsopGem
```bash
./run_isopgem.sh
```

That's it! IsopGem should launch in 10-15 seconds.

## 📋 System Requirements
- **OS**: Linux (64-bit)
- **RAM**: 2GB+ recommended
- **Disk**: 8GB free space
- **Display**: GUI desktop environment

## 🔧 Optional Setup

### Create Desktop Shortcut
```bash
./install_desktop_entry.sh
```

### Test Different Features
1. **Gematria**: Enter text and try different cipher methods
2. **Geometry**: Open the Regular Polygon Calculator
3. **Documents**: Load a text file and analyze it
4. **Astrology**: Enter birth data and generate a chart
5. **TQ**: Explore the ternary matrix

## 📝 Reporting Issues
- Check the `logs/` directory for error messages
- Include your Linux distribution and version
- Describe what you were doing when the issue occurred

## 📖 More Information
- See `README.md` for detailed information
- See `BETA_TESTING_GUIDE.md` for comprehensive testing instructions

---
**Happy Testing!** 🧪✨
